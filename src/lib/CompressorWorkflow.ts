import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from 'cloudflare:workers';
import { Env } from '../types';
import { UploadRecord } from './UploadRecord';

type Params = {
	name: string;
};

function sleep(ms: number) {
	return new Promise((res) => setTimeout(res, ms));
}

export class CompressorWorkflow extends WorkflowEntrypoint<Env, Params> {
	async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
		const container = this.env.COMPRESSOR.get(this.env.COMPRESSOR.idFromName(event.instanceId));
		const dbService = new UploadRecord(this.env.DB);

		await step.do('wait for container to be healthy and pass r2 object, and put on another object', async () => {
			const tries = 10;
			await container.init();

			const waitUntilContainerIsOk = async () => {
				let lastErr: unknown;
				for (let i = 0; i < tries; i++) {
					try {
						await container.logs();
						return;
					} catch (err) {
						console.error('transient error:', err instanceof Error ? err.message : JSON.stringify(err));
						await sleep(500);
						lastErr = err;
					}
				}

				throw lastErr;
			};

			await waitUntilContainerIsOk();

			const object = await this.env.BUCKET.get(event.payload.name);
			if (object === null) {
				console.error('Object not found: ' + event.payload.name);
				return;
			}

			try {
				const request = new Request('http://compressor', { method: 'POST', body: JSON.stringify({ name: event.payload.name }) });
				await container.fetch(request);

				const upload = await dbService.getByWorkflowInstanceId(event.instanceId);
				if (upload) {
					await dbService.update(upload.properties.upload_id, 'completed');
				}
			} catch (err) {
				console.error('There was an error compressing the object', err instanceof Error ? err.message : JSON.stringify(err));

				// Update status to failed on error
				const upload = await dbService.getByWorkflowInstanceId(event.instanceId);
				if (upload) {
					await dbService.update(upload.properties.upload_id, 'failed');
				}

				throw err;
			}
		});

		await step.do('destroy', async () => {
			await container.destroy();
		});
	}
}
